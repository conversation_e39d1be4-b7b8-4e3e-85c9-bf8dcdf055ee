# 使用wget下载数据集文件
mkdir -p evaluation_datasets

# 下载 openbookqa
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/allenai/openbookqa/resolve/main/ -P evaluation_datasets/openbookqa/

# 下载 mmlu
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/cais/mmlu/resolve/main/ -P evaluation_datasets/mmlu/

# 下载 gsm8k
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/openai/gsm8k/resolve/main/ -P evaluation_datasets/gsm8k/

# 下载 triviaqa
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/mandarjoshi/trivia_qa/resolve/main/ -P evaluation_datasets/triviaqa/

# 下载 hellaswag
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/Rowan/hellaswag/resolve/main/ -P evaluation_datasets/hellaswag/

# 下载 squad_v2
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/rajpurkar/squad_v2/resolve/main/ -P evaluation_datasets/squad_v2/

# 下载 math
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/hendrycks/competition_math/resolve/main/ -P evaluation_datasets/math/

# 下载 bbh
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/lukaemon/bbh/resolve/main/ -P evaluation_datasets/bbh/

# 下载 humaneval
wget -r -np -nH --cut-dirs=3 https://hf-mirror.com/datasets/openai/openai_humaneval/resolve/main/ -P evaluation_datasets/humaneval/
