#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RK3588 Qwen3-4B 评测系统配置文件
"""

import os

# 服务器配置
SERVER_CONFIG = {
    "base_url": "http://192.168.0.106:8080",
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 1.0
}

# 数据集配置
DATASETS_CONFIG = {
    "data_dir": "evaluation_datasets",
    "cache_dir": "cache",
    "download_timeout": 300,
    "max_samples_per_dataset": 10  # 每个数据集最大测试样本数
}

# 性能标准配置
PERFORMANCE_STANDARDS = {
    # 速度性能 (ms 或 tokens/s)
    "first_token_latency": {"excellent": 300, "good": 400, "minimum": 500},
    "token_generation_speed": {"excellent": 25, "good": 18, "minimum": 15},
    "batch_throughput": {"excellent": 80, "good": 60, "minimum": 50},
    "model_load_time": {"excellent": 5, "good": 8, "minimum": 10},
    "memory_allocation_time": {"excellent": 1, "good": 1.5, "minimum": 2},
    
    # 资源利用 (GB 或 %)
    "model_memory_usage": {"excellent": 1.5, "good": 1.8, "minimum": 2.0},
    "system_available_memory": {"excellent": 7, "good": 6.5, "minimum": 6},
    "cpu_utilization": {"excellent": 15, "good": 20, "minimum": 30},
    "memory_fragmentation": {"excellent": 3, "good": 5, "minimum": 10},
    
    # 功耗散热 (W 或 °C)
    "total_power": {"excellent": 6, "good": 8, "minimum": 10},
    "npu_power": {"excellent": 4, "good": 5, "minimum": 6},
    "chip_temperature": {"excellent": 60, "good": 65, "minimum": 70}
}

# 智商评测标准
IQ_STANDARDS = {
    "retention_rate": {
        "excellent": 0.99,  # 99%以上保持率
        "good": 0.98,       # 98%以上保持率
        "acceptable": 0.95, # 95%以上保持率
        "minimum": 0.92     # 92%以上保持率
    },
    "accuracy_threshold": {
        "high": 0.8,        # 高准确率阈值
        "medium": 0.6,      # 中等准确率阈值
        "low": 0.4          # 低准确率阈值
    }
}

# 评测模式配置
EVALUATION_MODES = {
    "quick": {
        "datasets": ["openbookqa", "mmlu", "gsm8k"],
        "samples_per_dataset": 5,
        "include_performance": False,
        "description": "快速验证测试，使用3个核心数据集"
    },
    "standard": {
        "datasets": ["openbookqa", "triviaqa", "hellaswag", "squad_v2", "mmlu", "gsm8k"],
        "samples_per_dataset": 5,
        "include_performance": True,
        "description": "标准评测，使用6个主要数据集"
    },
    "comprehensive": {
        "datasets": ["openbookqa", "triviaqa", "hellaswag", "squad_v2", "xwino", 
                    "mmlu", "gsm8k", "math", "bbh", "humaneval"],
        "samples_per_dataset": 5,
        "include_performance": True,
        "description": "完整评测，使用全部10个数据集"
    }
}

# 输出配置
OUTPUT_CONFIG = {
    "results_dir": "results",
    "reports_dir": "reports",
    "logs_dir": "logs",
    "timestamp_format": "%Y%m%d_%H%M%S",
    "encoding": "utf-8"
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file_rotation": True,
    "max_file_size": "10MB",
    "backup_count": 5
}

# 硬件平台配置
PLATFORM_CONFIG = {
    "name": "RK3588",
    "cpu_cores": 8,
    "memory_total": 8,  # GB
    "npu_available": True,
    "gpu_available": False,
    "target_platform": "rk3588"
}

# 模型配置
MODEL_CONFIG = {
    "name": "Qwen3-1.7B",
    "model_size": "1.7B",
    "quantization": "int8",
    "max_context_length": 2048,
    "max_new_tokens": 512,
    "model_file": "Qwen3-1.7B.rkllm",
    "platform": "RK3588"
}

# 环境变量配置
def get_env_config():
    """获取环境变量配置"""
    return {
        "server_url": os.getenv("RKLLM_SERVER_URL", SERVER_CONFIG["base_url"]),
        "data_dir": os.getenv("EVALUATION_DATA_DIR", DATASETS_CONFIG["data_dir"]),
        "results_dir": os.getenv("EVALUATION_RESULTS_DIR", OUTPUT_CONFIG["results_dir"]),
        "log_level": os.getenv("LOG_LEVEL", LOGGING_CONFIG["level"]),
        "max_samples": int(os.getenv("MAX_SAMPLES_PER_DATASET", DATASETS_CONFIG["max_samples_per_dataset"]))
    }

# 验证配置
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必要的目录
    required_dirs = [
        DATASETS_CONFIG["data_dir"],
        OUTPUT_CONFIG["results_dir"],
        OUTPUT_CONFIG["reports_dir"]
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建目录 {dir_path}: {e}")
    
    # 检查性能标准的合理性
    for metric, standards in PERFORMANCE_STANDARDS.items():
        if not all(key in standards for key in ["excellent", "good", "minimum"]):
            errors.append(f"性能标准 {metric} 缺少必要的等级定义")
    
    # 检查评测模式配置
    for mode, config in EVALUATION_MODES.items():
        if not all(key in config for key in ["datasets", "samples_per_dataset"]):
            errors.append(f"评测模式 {mode} 配置不完整")
    
    return errors

# 获取完整配置
def get_full_config():
    """获取完整的配置字典"""
    env_config = get_env_config()
    
    return {
        "server": SERVER_CONFIG,
        "datasets": DATASETS_CONFIG,
        "performance_standards": PERFORMANCE_STANDARDS,
        "iq_standards": IQ_STANDARDS,
        "evaluation_modes": EVALUATION_MODES,
        "output": OUTPUT_CONFIG,
        "logging": LOGGING_CONFIG,
        "platform": PLATFORM_CONFIG,
        "model": MODEL_CONFIG,
        "environment": env_config
    }

# 配置初始化
def init_config():
    """初始化配置"""
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    # 创建必要的目录
    env_config = get_env_config()
    os.makedirs(env_config["data_dir"], exist_ok=True)
    os.makedirs(env_config["results_dir"], exist_ok=True)
    os.makedirs(OUTPUT_CONFIG["reports_dir"], exist_ok=True)
    os.makedirs(OUTPUT_CONFIG["logs_dir"], exist_ok=True)
    
    print("配置初始化完成")
    return True

if __name__ == "__main__":
    # 测试配置
    print("=== RK3588 Qwen3-1.7B 评测系统配置 ===")
    
    if init_config():
        config = get_full_config()
        
        print(f"服务器地址: {config['environment']['server_url']}")
        print(f"数据目录: {config['environment']['data_dir']}")
        print(f"结果目录: {config['environment']['results_dir']}")
        print(f"平台: {config['platform']['name']}")
        print(f"模型: {config['model']['name']}")
        
        print("\n可用评测模式:")
        for mode, mode_config in config['evaluation_modes'].items():
            print(f"  - {mode}: {mode_config['description']}")
        
        print("\n配置验证通过 ✓")
    else:
        print("\n配置验证失败 ✗")
