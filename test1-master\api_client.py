#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API客户端模块
用于与flask_server.py进行通信，支持模型推理和性能监控
"""

import requests
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class InferenceResult:
    """推理结果数据类"""
    response: str
    latency: float  # 延迟时间（秒）
    tokens_per_second: float  # Token生成速度
    success: bool
    error_message: Optional[str] = None

class RKLLMAPIClient:
    """RKLLM API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8080", timeout: int = 60):
        """
        初始化API客户端
        
        Args:
            base_url: Flask服务器地址
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def health_check(self) -> bool:
        """
        检查服务器健康状态

        Returns:
            服务器是否可用
        """
        try:
            # 使用简单的测试请求来检查服务器状态
            test_data = {
                "messages": [{"role": "user", "content": "test"}],
                "stream": False
            }
            response = self.session.post(f"{self.base_url}/rkllm_chat", json=test_data, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       stream: bool = False,
                       enable_thinking: bool = False,
                       tools: Optional[List[Dict]] = None) -> InferenceResult:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "问题"}]
            stream: 是否使用流式响应
            enable_thinking: 是否启用思考模式
            tools: 工具列表（可选）
            
        Returns:
            推理结果
        """
        start_time = time.time()
        
        payload = {
            "messages": messages,
            "stream": stream,
            "enable_thinking": enable_thinking
        }
        
        if tools:
            payload["tools"] = tools
        
        try:
            response = self.session.post(
                f"{self.base_url}/rkllm_chat",
                json=payload,
                timeout=self.timeout
            )
            
            end_time = time.time()
            latency = end_time - start_time
            
            if response.status_code == 200:
                result_data = response.json()
                
                # 提取响应内容
                if "choices" in result_data and len(result_data["choices"]) > 0:
                    content = result_data["choices"][0]["message"]["content"]
                    
                    # 估算Token生成速度（简单估算：按字符数/4）
                    estimated_tokens = len(content) / 4
                    tokens_per_second = estimated_tokens / latency if latency > 0 else 0
                    
                    return InferenceResult(
                        response=content,
                        latency=latency,
                        tokens_per_second=tokens_per_second,
                        success=True
                    )
                else:
                    return InferenceResult(
                        response="",
                        latency=latency,
                        tokens_per_second=0,
                        success=False,
                        error_message="响应格式错误"
                    )
            
            elif response.status_code == 503:
                return InferenceResult(
                    response="",
                    latency=latency,
                    tokens_per_second=0,
                    success=False,
                    error_message="服务器忙碌，请稍后重试"
                )
            else:
                return InferenceResult(
                    response="",
                    latency=latency,
                    tokens_per_second=0,
                    success=False,
                    error_message=f"HTTP错误: {response.status_code}"
                )
                
        except requests.exceptions.Timeout:
            return InferenceResult(
                response="",
                latency=self.timeout,
                tokens_per_second=0,
                success=False,
                error_message="请求超时"
            )
        except Exception as e:
            return InferenceResult(
                response="",
                latency=time.time() - start_time,
                tokens_per_second=0,
                success=False,
                error_message=f"请求失败: {str(e)}"
            )
    
    def simple_chat(self, question: str, system_prompt: Optional[str] = None) -> InferenceResult:
        """
        简单聊天接口
        
        Args:
            question: 用户问题
            system_prompt: 系统提示（可选）
            
        Returns:
            推理结果
        """
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": question})
        
        return self.chat_completion(messages)
    
    def batch_inference(self, questions: List[str], 
                       system_prompt: Optional[str] = None,
                       delay_between_requests: float = 0.1) -> List[InferenceResult]:
        """
        批量推理
        
        Args:
            questions: 问题列表
            system_prompt: 系统提示（可选）
            delay_between_requests: 请求间延迟（秒）
            
        Returns:
            推理结果列表
        """
        results = []
        
        for i, question in enumerate(questions):
            logger.info(f"处理问题 {i+1}/{len(questions)}: {question[:50]}...")
            
            result = self.simple_chat(question, system_prompt)
            results.append(result)
            
            if result.success:
                logger.info(f"✓ 推理成功，延迟: {result.latency:.2f}s, 速度: {result.tokens_per_second:.1f} tokens/s")
            else:
                logger.error(f"✗ 推理失败: {result.error_message}")
            
            # 添加延迟避免服务器过载
            if i < len(questions) - 1:
                time.sleep(delay_between_requests)
        
        return results
    
    def performance_test(self, test_prompt: str = "请介绍一下人工智能的发展历史。", 
                        num_tests: int = 5) -> Dict[str, float]:
        """
        性能测试
        
        Args:
            test_prompt: 测试提示
            num_tests: 测试次数
            
        Returns:
            性能统计数据
        """
        logger.info(f"开始性能测试，测试 {num_tests} 次...")
        
        latencies = []
        token_speeds = []
        success_count = 0
        
        for i in range(num_tests):
            logger.info(f"执行第 {i+1}/{num_tests} 次测试...")
            
            result = self.simple_chat(test_prompt)
            
            if result.success:
                latencies.append(result.latency)
                token_speeds.append(result.tokens_per_second)
                success_count += 1
            else:
                logger.error(f"第 {i+1} 次测试失败: {result.error_message}")
            
            time.sleep(0.5)  # 测试间隔
        
        if success_count == 0:
            logger.error("所有测试都失败了")
            return {}
        
        # 计算统计数据
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        avg_token_speed = sum(token_speeds) / len(token_speeds)
        success_rate = success_count / num_tests
        
        stats = {
            "average_latency": avg_latency,
            "min_latency": min_latency,
            "max_latency": max_latency,
            "average_token_speed": avg_token_speed,
            "success_rate": success_rate,
            "total_tests": num_tests,
            "successful_tests": success_count
        }
        
        logger.info("=== 性能测试结果 ===")
        logger.info(f"平均延迟: {avg_latency:.2f}s")
        logger.info(f"最小延迟: {min_latency:.2f}s")
        logger.info(f"最大延迟: {max_latency:.2f}s")
        logger.info(f"平均Token速度: {avg_token_speed:.1f} tokens/s")
        logger.info(f"成功率: {success_rate:.1%}")
        
        return stats
    
    def close(self):
        """关闭客户端连接"""
        self.session.close()

if __name__ == "__main__":
    # 测试代码
    client = RKLLMAPIClient()
    
    # 健康检查
    if client.health_check():
        print("✓ 服务器连接正常")
        
        # 简单测试
        result = client.simple_chat("你好，请介绍一下你自己。")
        if result.success:
            print(f"✓ 推理成功: {result.response[:100]}...")
            print(f"延迟: {result.latency:.2f}s, 速度: {result.tokens_per_second:.1f} tokens/s")
        else:
            print(f"✗ 推理失败: {result.error_message}")
        
        # 性能测试
        # stats = client.performance_test(num_tests=3)
        
    else:
        print("✗ 服务器连接失败，请确保flask_server.py正在运行")
    
    client.close()
