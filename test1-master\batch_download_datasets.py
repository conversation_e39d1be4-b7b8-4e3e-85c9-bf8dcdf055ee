#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量下载数据集脚本
按照evaluation_datasets.md中的计划下载数据集文件
"""

from datasets import load_dataset
import os
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'dataset_download_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

def download_datasets():
    """按照evaluation_datasets.md中的计划下载数据集"""
    
    logger.info("=" * 60)
    logger.info("开始按照evaluation_datasets.md计划下载数据集")
    logger.info("=" * 60)
    
    # 创建数据目录
    os.makedirs("evaluation_datasets", exist_ok=True)
    logger.info("数据目录已创建: evaluation_datasets/")
    
    # 数据集列表（来自evaluation_datasets.md）
    datasets_info = {
        "openbookqa": "allenai/openbookqa",
        "triviaqa": "mandarjoshi/trivia_qa", 
        "hellaswag": "Rowan/hellaswag",
        "squad_v2": "rajpurkar/squad_v2",
        "mmlu": "cais/mmlu",
        "gsm8k": "openai/gsm8k",
        "math": "hendrycks/competition_math",
        "bbh": "lukaemon/bbh",
        "humaneval": "openai/openai_humaneval"
    }
    
    # 数据集大小信息（来自evaluation_datasets.md）
    dataset_sizes = {
        "openbookqa": "1.45 MB",
        "triviaqa": "2.67 GB",
        "bbh": "647 kB"
    }
    
    # 备用下载地址
    backup_datasets = {
        "triviaqa": "lucadiliello/triviaqa",
        "hellaswag": "alexandrainst/m_hellaswag",
        "squad_v2": "GEM/squad_v2",
        "math": "EleutherAI/hendrycks_math",
        "humaneval": "bigcode/humanevalpack"
    }
    
    success_count = 0
    total_count = len(datasets_info)
    download_results = {}
    
    # 批量下载
    for i, (name, dataset_id) in enumerate(datasets_info.items(), 1):
        logger.info(f"\n[{i}/{total_count}] 正在下载 {name}...")
        
        # 显示预计大小
        if name in dataset_sizes:
            logger.info(f"预计大小: {dataset_sizes[name]}")
        
        # 检查是否已存在
        dataset_path = f"evaluation_datasets/{name}"
        if os.path.exists(dataset_path):
            logger.info(f"✓ {name} 已存在，跳过下载")
            download_results[name] = "已存在"
            success_count += 1
            continue
        
        try:
            start_time = time.time()
            
            # 尝试主要地址
            logger.info(f"从 {dataset_id} 下载...")
            dataset = load_dataset(dataset_id)
            dataset.save_to_disk(dataset_path)
            
            end_time = time.time()
            download_time = round(end_time - start_time, 2)
            
            logger.info(f"✓ {name} 下载完成 (耗时: {download_time}秒)")
            download_results[name] = f"成功 ({download_time}秒)"
            success_count += 1
            
        except Exception as e:
            logger.warning(f"主要地址下载失败: {e}")
            
            # 尝试备用地址
            if name in backup_datasets:
                try:
                    backup_id = backup_datasets[name]
                    logger.info(f"尝试备用地址: {backup_id}")
                    
                    start_time = time.time()
                    dataset = load_dataset(backup_id)
                    dataset.save_to_disk(dataset_path)
                    end_time = time.time()
                    download_time = round(end_time - start_time, 2)
                    
                    logger.info(f"✓ {name} 从备用地址下载完成 (耗时: {download_time}秒)")
                    download_results[name] = f"备用成功 ({download_time}秒)"
                    success_count += 1
                    
                except Exception as e2:
                    logger.error(f"✗ {name} 下载失败: {e2}")
                    download_results[name] = f"失败: {e2}"
            else:
                logger.error(f"✗ {name} 下载失败: {e}")
                download_results[name] = f"失败: {e}"
    
    # 下载完成统计
    logger.info("=" * 60)
    logger.info("数据集下载完成统计:")
    logger.info("=" * 60)
    
    for name, result in download_results.items():
        status_icon = "✅" if "成功" in result or "已存在" in result else "❌"
        logger.info(f"{status_icon} {name}: {result}")
    
    logger.info("=" * 60)
    logger.info(f"下载完成: {success_count}/{total_count} 个数据集成功")
    success_rate = round((success_count / total_count) * 100, 1)
    logger.info(f"成功率: {success_rate}%")
    logger.info("=" * 60)
    
    # 保存下载结果
    import json
    result_summary = {
        "timestamp": datetime.now().isoformat(),
        "total_datasets": total_count,
        "successful_downloads": success_count,
        "success_rate": success_rate,
        "download_results": download_results,
        "datasets_info": datasets_info
    }
    
    os.makedirs("results", exist_ok=True)
    result_file = f"results/batch_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result_summary, f, ensure_ascii=False, indent=2)
    
    logger.info(f"下载结果已保存到: {result_file}")
    
    if success_count == total_count:
        logger.info("🎉 所有数据集下载完成！")
    elif success_count > 0:
        logger.info(f"⚠️ 部分数据集下载完成，{total_count - success_count}个失败")
    else:
        logger.error("❌ 所有数据集下载失败")
    
    return download_results

def check_datasets():
    """检查已下载的数据集"""
    logger.info("检查已下载的数据集...")
    
    datasets_info = {
        "openbookqa": "allenai/openbookqa",
        "triviaqa": "mandarjoshi/trivia_qa", 
        "hellaswag": "Rowan/hellaswag",
        "squad_v2": "rajpurkar/squad_v2",
        "mmlu": "cais/mmlu",
        "gsm8k": "openai/gsm8k",
        "math": "hendrycks/competition_math",
        "bbh": "lukaemon/bbh",
        "humaneval": "openai/openai_humaneval"
    }
    
    for name in datasets_info.keys():
        dataset_path = f"evaluation_datasets/{name}"
        if os.path.exists(dataset_path):
            # 检查目录大小
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(dataset_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
            
            size_mb = round(total_size / (1024 * 1024), 2)
            logger.info(f"✅ {name}: 已下载 ({size_mb} MB)")
        else:
            logger.info(f"❌ {name}: 未下载")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="批量下载数据集")
    parser.add_argument("--action", choices=["download", "check"], 
                       default="download", help="执行动作")
    
    args = parser.parse_args()
    
    if args.action == "download":
        download_datasets()
    elif args.action == "check":
        check_datasets()

if __name__ == "__main__":
    main()
