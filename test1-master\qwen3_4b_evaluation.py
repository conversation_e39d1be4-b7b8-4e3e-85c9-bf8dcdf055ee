#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3-4B完整智商评测系统
执行全部10项智商测试并生成详细报告
"""

import os
import sys
import time
import json
import argparse
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
import logging

# 导入自定义模块
from evaluation_datasets import EvaluationDatasets
from api_client import R<PERSON><PERSON><PERSON>PIClient, InferenceResult
from performance_monitor import PerformanceMonitor, PerformanceMetrics
from quick_test import QuickEvaluator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveEvaluator:
    """完整智商评测器"""
    
    def __init__(self, api_client: RKLLMAPIClient, datasets_manager: EvaluationDatasets):
        """
        初始化完整评测器
        
        Args:
            api_client: API客户端
            datasets_manager: 数据集管理器
        """
        self.api_client = api_client
        self.datasets_manager = datasets_manager
        self.performance_monitor = PerformanceMonitor(api_client)
        
        # 所有数据集列表
        self.all_datasets = [
            "openbookqa", "triviaqa", "hellaswag", "squad_v2", "xwino",
            "mmlu", "gsm8k", "math", "bbh", "humaneval"
        ]
        
        # 评测提示模板
        self.prompts = {
            "openbookqa": "请回答以下科学常识选择题。只需要回答选项字母（A、B、C或D）。\n\n问题：{question}\n选项：\n{choices}\n\n答案：",
            "triviaqa": "请回答以下知识问答题。请给出准确的答案。\n\n问题：{question}\n\n答案：",
            "hellaswag": "请选择最合理的句子续写。只需要回答选项编号（0、1、2或3）。\n\n情境：{context}\n选项：\n{endings}\n\n答案：",
            "squad_v2": "请根据以下文本回答问题。如果文本中没有答案，请回答'无法回答'。\n\n文本：{context}\n\n问题：{question}\n\n答案：",
            "xwino": "请判断句子中的代词指代哪个选项。只需要回答选项编号（1或2）。\n\n句子：{sentence}\n代词：{pronoun}\n选项1：{option1}\n选项2：{option2}\n\n答案：",
            "mmlu": "请回答以下{subject}领域的选择题。只需要回答选项字母（A、B、C或D）。\n\n问题：{question}\n选项：\n{choices}\n\n答案：",
            "gsm8k": "请解答以下数学应用题，并给出最终的数值答案。\n\n问题：{question}\n\n请逐步解答：",
            "math": "请解答以下数学竞赛题。这是一道{type}类型的{level}级题目。\n\n问题：{problem}\n\n请详细解答：",
            "bbh": "请回答以下复杂推理问题。\n\n问题：{input}\n\n答案：",
            "humaneval": "请完成以下Python函数。只需要返回函数实现代码。\n\n{prompt}\n\n请实现："
        }
    
    def format_choices(self, choices: List[str]) -> str:
        """格式化选择题选项"""
        choice_letters = ['A', 'B', 'C', 'D']
        formatted = []
        for i, choice in enumerate(choices):
            if i < len(choice_letters):
                formatted.append(f"{choice_letters[i]}. {choice}")
        return "\n".join(formatted)
    
    def format_endings(self, endings: List[str]) -> str:
        """格式化HellaSwag选项"""
        formatted = []
        for i, ending in enumerate(endings):
            formatted.append(f"{i}. {ending}")
        return "\n".join(formatted)
    
    def evaluate_dataset(self, dataset_name: str) -> Tuple[float, List[Dict], Dict[str, Any]]:
        """
        评测单个数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            (准确率, 详细结果, 统计信息)
        """
        logger.info(f"开始评测 {dataset_name.upper()}...")
        
        # 获取测试样本
        samples = self.datasets_manager.get_test_samples(dataset_name)
        if not samples:
            logger.error(f"无法获取 {dataset_name} 的测试样本")
            return 0.0, [], {}
        
        correct = 0
        total = len(samples)
        results = []
        latencies = []
        
        for i, sample in enumerate(samples):
            logger.info(f"处理 {dataset_name} 问题 {i+1}/{total}")
            
            # 构建提示
            prompt = self._build_prompt(dataset_name, sample)
            if not prompt:
                continue
            
            # 调用模型
            result = self.api_client.simple_chat(prompt)
            latencies.append(result.latency)
            
            if result.success:
                # 评估答案
                is_correct = self._evaluate_answer(dataset_name, sample, result.response)
                if is_correct:
                    correct += 1
                
                results.append({
                    "sample_id": i,
                    "question": self._extract_question(dataset_name, sample),
                    "correct_answer": self._extract_correct_answer(dataset_name, sample),
                    "predicted_answer": self._extract_predicted_answer(dataset_name, result.response),
                    "is_correct": is_correct,
                    "response": result.response,
                    "latency": result.latency,
                    "tokens_per_second": result.tokens_per_second
                })
                
                logger.info(f"{'✓' if is_correct else '✗'} 第{i+1}题")
            else:
                logger.error(f"推理失败: {result.error_message}")
                results.append({
                    "sample_id": i,
                    "question": self._extract_question(dataset_name, sample),
                    "correct_answer": self._extract_correct_answer(dataset_name, sample),
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "response": result.error_message,
                    "latency": result.latency,
                    "tokens_per_second": 0
                })
        
        accuracy = correct / total if total > 0 else 0
        
        # 统计信息
        stats = {
            "total_samples": total,
            "correct_answers": correct,
            "accuracy": accuracy,
            "average_latency": sum(latencies) / len(latencies) if latencies else 0,
            "total_time": sum(latencies),
            "success_rate": len([r for r in results if r["is_correct"] is not False]) / total if total > 0 else 0
        }
        
        logger.info(f"{dataset_name} 评测完成: 准确率={accuracy:.3f} ({correct}/{total})")
        
        return accuracy, results, stats
    
    def _build_prompt(self, dataset_name: str, sample: Dict[str, Any]) -> str:
        """构建提示"""
        try:
            if dataset_name == "openbookqa":
                choices_text = self.format_choices(sample["choices"])
                return self.prompts[dataset_name].format(
                    question=sample["question"],
                    choices=choices_text
                )
            elif dataset_name == "triviaqa":
                return self.prompts[dataset_name].format(question=sample["question"])
            elif dataset_name == "hellaswag":
                endings_text = self.format_endings(sample["endings"])
                return self.prompts[dataset_name].format(
                    context=sample["context"],
                    endings=endings_text
                )
            elif dataset_name == "squad_v2":
                return self.prompts[dataset_name].format(
                    context=sample["context"],
                    question=sample["question"]
                )
            elif dataset_name == "xwino":
                return self.prompts[dataset_name].format(
                    sentence=sample["sentence"],
                    pronoun=sample["pronoun"],
                    option1=sample["option1"],
                    option2=sample["option2"]
                )
            elif dataset_name == "mmlu":
                choices_text = self.format_choices(sample["choices"])
                return self.prompts[dataset_name].format(
                    subject=sample.get("subject", "通用"),
                    question=sample["question"],
                    choices=choices_text
                )
            elif dataset_name == "gsm8k":
                return self.prompts[dataset_name].format(question=sample["question"])
            elif dataset_name == "math":
                return self.prompts[dataset_name].format(
                    type=sample.get("type", "数学"),
                    level=sample.get("level", "中等"),
                    problem=sample["problem"]
                )
            elif dataset_name == "bbh":
                return self.prompts[dataset_name].format(input=sample["input"])
            elif dataset_name == "humaneval":
                return self.prompts[dataset_name].format(prompt=sample["prompt"])
            else:
                logger.error(f"未知数据集: {dataset_name}")
                return ""
        except Exception as e:
            logger.error(f"构建提示失败: {e}")
            return ""
    
    def _extract_question(self, dataset_name: str, sample: Dict[str, Any]) -> str:
        """提取问题文本"""
        if dataset_name in ["openbookqa", "triviaqa", "squad_v2", "mmlu", "gsm8k"]:
            return sample.get("question", "")
        elif dataset_name == "hellaswag":
            return sample.get("context", "")
        elif dataset_name == "xwino":
            return sample.get("sentence", "")
        elif dataset_name == "math":
            return sample.get("problem", "")
        elif dataset_name == "bbh":
            return sample.get("input", "")
        elif dataset_name == "humaneval":
            return sample.get("prompt", "")
        return ""
    
    def _extract_correct_answer(self, dataset_name: str, sample: Dict[str, Any]) -> Any:
        """提取正确答案"""
        if dataset_name in ["openbookqa", "mmlu"]:
            return sample.get("answer", "")
        elif dataset_name == "triviaqa":
            return sample.get("answer", "")
        elif dataset_name == "hellaswag":
            return sample.get("answer", 0)
        elif dataset_name == "squad_v2":
            answers = sample.get("answers", [])
            return answers[0] if answers else "无法回答"
        elif dataset_name == "xwino":
            return sample.get("answer", "")
        elif dataset_name == "gsm8k":
            return self._extract_numeric_answer(sample.get("answer", ""))
        elif dataset_name == "math":
            return sample.get("solution", "")
        elif dataset_name == "bbh":
            return sample.get("target", "")
        elif dataset_name == "humaneval":
            return sample.get("canonical_solution", "")
        return ""
    
    def _extract_predicted_answer(self, dataset_name: str, response: str) -> Any:
        """提取预测答案"""
        if dataset_name in ["openbookqa", "mmlu"]:
            return self._extract_choice_answer(response)
        elif dataset_name == "hellaswag":
            return self._extract_numeric_choice(response)
        elif dataset_name == "xwino":
            return self._extract_binary_choice(response)
        elif dataset_name == "gsm8k":
            return self._extract_numeric_answer(response)
        else:
            return response.strip()
    
    def _evaluate_answer(self, dataset_name: str, sample: Dict[str, Any], response: str) -> bool:
        """评估答案正确性"""
        correct_answer = self._extract_correct_answer(dataset_name, sample)
        predicted_answer = self._extract_predicted_answer(dataset_name, response)
        
        if dataset_name in ["openbookqa", "mmlu"]:
            return predicted_answer == correct_answer
        elif dataset_name == "hellaswag":
            return predicted_answer == correct_answer
        elif dataset_name == "xwino":
            return predicted_answer == correct_answer
        elif dataset_name == "gsm8k":
            if isinstance(predicted_answer, (int, float)) and isinstance(correct_answer, (int, float)):
                return abs(predicted_answer - correct_answer) < 0.01
            return False
        elif dataset_name in ["triviaqa", "squad_v2", "math", "bbh", "humaneval"]:
            # 对于开放式问题，使用简单的字符串匹配（实际应用中可能需要更复杂的评估）
            return str(correct_answer).lower() in str(predicted_answer).lower()
        
        return False
    
    def _extract_choice_answer(self, response: str) -> str:
        """提取选择题答案"""
        response = response.strip().upper()
        for choice in ['A', 'B', 'C', 'D']:
            if choice in response:
                return choice
        return "UNKNOWN"
    
    def _extract_numeric_choice(self, response: str) -> int:
        """提取数字选择答案"""
        import re
        matches = re.findall(r'\b([0-3])\b', response)
        if matches:
            return int(matches[0])
        return -1
    
    def _extract_binary_choice(self, response: str) -> str:
        """提取二元选择答案"""
        if "1" in response:
            return "option1"
        elif "2" in response:
            return "option2"
        return "UNKNOWN"
    
    def _extract_numeric_answer(self, text: str) -> Optional[float]:
        """提取数值答案"""
        import re
        patterns = [
            r'答案是\s*([+-]?\d+\.?\d*)',
            r'答案：\s*([+-]?\d+\.?\d*)',
            r'=\s*([+-]?\d+\.?\d*)',
            r'([+-]?\d+\.?\d*)\s*$',
            r'([+-]?\d+\.?\d*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    return float(matches[-1])
                except ValueError:
                    continue
        return None
    
    def run_comprehensive_evaluation(self) -> Dict[str, Any]:
        """运行完整评测"""
        logger.info("=== 开始完整智商评测 ===")
        
        # 检查服务器连接
        if not self.api_client.health_check():
            logger.error("无法连接到RKLLM服务器")
            return {"error": "服务器连接失败"}
        
        results = {}
        baseline_scores = self.datasets_manager.get_baseline_scores()
        
        # 评测所有数据集
        for dataset_name in self.all_datasets:
            logger.info(f"\n=== 评测 {dataset_name.upper()} ===")
            
            try:
                accuracy, details, stats = self.evaluate_dataset(dataset_name)
                
                # 计算保持率
                baseline = baseline_scores.get(dataset_name, 0)
                retention_rate = accuracy / baseline if baseline > 0 else 0
                
                results[dataset_name] = {
                    "accuracy": accuracy,
                    "baseline_score": baseline,
                    "retention_rate": retention_rate,
                    "statistics": stats,
                    "details": details
                }
                
                logger.info(f"{dataset_name} 完成: 准确率={accuracy:.3f}, 保持率={retention_rate:.1%}")
                
            except Exception as e:
                logger.error(f"评测 {dataset_name} 时发生错误: {e}")
                results[dataset_name] = {
                    "error": str(e),
                    "accuracy": 0,
                    "baseline_score": baseline_scores.get(dataset_name, 0),
                    "retention_rate": 0
                }
        
        # 计算总体统计
        valid_results = {k: v for k, v in results.items() if "error" not in v}
        if valid_results:
            avg_accuracy = sum(r["accuracy"] for r in valid_results.values()) / len(valid_results)
            avg_retention = sum(r["retention_rate"] for r in valid_results.values()) / len(valid_results)
            
            # 评估智商等级
            if avg_retention >= 0.99:
                iq_level = "优秀"
                iq_description = "智商能力基本无损失"
            elif avg_retention >= 0.98:
                iq_level = "良好"
                iq_description = "智商能力轻微下降"
            elif avg_retention >= 0.95:
                iq_level = "合格"
                iq_description = "智商能力可接受下降"
            else:
                iq_level = "不合格"
                iq_description = "智商能力下降过多"
            
            results["summary"] = {
                "average_accuracy": avg_accuracy,
                "average_retention_rate": avg_retention,
                "datasets_tested": len(valid_results),
                "datasets_failed": len(results) - len(valid_results),
                "iq_level": iq_level,
                "iq_description": iq_description,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        
        logger.info("=== 完整智商评测完成 ===")
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Qwen3-4B完整智商评测系统")
    parser.add_argument("--server-url", default="http://localhost:8080", help="RKLLM服务器地址")
    parser.add_argument("--output", default="comprehensive_evaluation_results.json", help="结果输出文件")
    parser.add_argument("--report", default="evaluation_report.md", help="报告输出文件")
    parser.add_argument("--data-dir", default="evaluation_datasets", help="数据集目录")
    parser.add_argument("--performance-test", action="store_true", help="是否进行性能测试")
    
    args = parser.parse_args()
    
    # 初始化组件
    api_client = RKLLMAPIClient(base_url=args.server_url)
    datasets_manager = EvaluationDatasets(data_dir=args.data_dir)
    evaluator = ComprehensiveEvaluator(api_client, datasets_manager)
    
    try:
        # 运行完整评测
        results = evaluator.run_comprehensive_evaluation()
        
        # 保存结果
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"评测结果已保存到: {args.output}")
        
        # 生成报告
        if args.performance_test:
            # 进行性能测试
            test_prompts = [
                "请介绍一下人工智能的发展历史。",
                "解释一下机器学习和深度学习的区别。",
                "什么是大语言模型？它有哪些应用？"
            ]
            performance_metrics = evaluator.performance_monitor.measure_inference_performance(test_prompts)
            report = evaluator.performance_monitor.generate_performance_report(performance_metrics, results)
        else:
            # 仅生成智商评测报告
            report = generate_evaluation_report(results)
        
        with open(args.report, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"评测报告已保存到: {args.report}")
        
    except KeyboardInterrupt:
        logger.info("评测被用户中断")
    except Exception as e:
        logger.error(f"评测过程中发生错误: {e}")
    finally:
        api_client.close()

def generate_evaluation_report(results: Dict[str, Any]) -> str:
    """生成评测报告"""
    if "summary" not in results:
        return "# 评测报告\n\n评测未完成或发生错误。"
    
    summary = results["summary"]
    
    report = f"""# Qwen3-4B 智商评测报告

## 评测概况

- **评测时间**: {summary.get('timestamp', 'N/A')}
- **测试数据集**: {summary.get('datasets_tested', 0)}个
- **失败数据集**: {summary.get('datasets_failed', 0)}个
- **平均准确率**: {summary.get('average_accuracy', 0):.3f}
- **平均保持率**: {summary.get('average_retention_rate', 0):.1%}
- **智商等级**: {summary.get('iq_level', 'N/A')}
- **评价**: {summary.get('iq_description', 'N/A')}

## 详细结果

| 数据集 | 准确率 | 基准分数 | 保持率 | 样本数 | 状态 |
|--------|--------|----------|--------|--------|------|"""
    
    for dataset_name, result in results.items():
        if dataset_name != "summary":
            if "error" in result:
                report += f"\n| {dataset_name.upper()} | - | {result.get('baseline_score', 0):.3f} | - | - | 失败 |"
            else:
                stats = result.get("statistics", {})
                report += f"\n| {dataset_name.upper()} | {result['accuracy']:.3f} | {result['baseline_score']:.3f} | {result['retention_rate']:.1%} | {stats.get('total_samples', 0)} | 完成 |"
    
    report += f"""

## 评测标准

- **优秀**: 平均保持率 > 99%，智商能力基本无损失
- **良好**: 平均保持率 > 98%，智商能力轻微下降  
- **合格**: 平均保持率 > 95%，智商能力可接受下降
- **不合格**: 平均保持率 < 95%，智商能力下降过多

---
*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
    
    return report

if __name__ == "__main__":
    main()
