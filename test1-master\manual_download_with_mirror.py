#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用镜像站点下载数据集
"""

import os
import logging
from datasets import load_dataset

# 设置镜像站点
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_with_mirror():
    """使用镜像站点下载"""
    datasets_info = {
        "openbookqa": "allenai/openbookqa",
        "mmlu": "cais/mmlu",
        "gsm8k": "openai/gsm8k",
        "triviaqa": "mandarjoshi/trivia_qa",
        "hellaswag": "Rowan/hellaswag",
        "squad_v2": "rajpurkar/squad_v2",
        "math": "hendrycks/competition_math",
        "bbh": "lukaemon/bbh",
        "humaneval": "openai/openai_humaneval"
    }
    
    os.makedirs("evaluation_datasets", exist_ok=True)
    
    for name, dataset_id in datasets_info.items():
        try:
            logger.info(f"正在从镜像下载 {name}...")
            dataset = load_dataset(dataset_id)
            dataset.save_to_disk(f"evaluation_datasets/{name}")
            logger.info(f"✓ {name} 下载完成")
        except Exception as e:
            logger.error(f"✗ {name} 下载失败: {e}")

if __name__ == "__main__":
    download_with_mirror()
