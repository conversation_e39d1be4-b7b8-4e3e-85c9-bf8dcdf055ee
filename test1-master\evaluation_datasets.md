# 大语言模型评测数据集下载地址

## 数据集列表

### 1. OpenBookQA
- **描述**: 科学知识和常识问答数据集
- **Hugging Face**: https://huggingface.co/datasets/allenai/openbookqa
- **大小**: 1.45 MB
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("allenai/openbookqa")
  ```

### 2. TriviaQA
- **描述**: 大规模远程监督阅读理解挑战数据集
- **Hugging Face**: https://huggingface.co/datasets/mandarjoshi/trivia_qa
- **备选**: https://huggingface.co/datasets/lucadiliello/triviaqa
- **大小**: 2.67 GB
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("mandarjoshi/trivia_qa")
  ```

### 3. HellaSwag
- **描述**: 常识自然语言推理数据集，用于句子完成任务
- **Hugging Face**: https://huggingface.co/datasets/Rowan/hellaswag
- **多语言版本**: https://huggingface.co/datasets/alexandrainst/m_hellaswag
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("Rowan/hellaswag")
  ```

### 4. SQuAD2
- **描述**: Stanford问答数据集2.0版本，包含无答案问题
- **Hugging Face**: https://huggingface.co/datasets/rajpurkar/squad_v2
- **备选**: https://huggingface.co/datasets/GEM/squad_v2
- **原版SQuAD**: https://huggingface.co/datasets/rajpurkar/squad
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("rajpurkar/squad_v2")
  ```

### 5. XWINO
- **描述**: 跨语言Winograd模式数据集，测试代词指代推理
- **GitHub**: https://github.com/yandex-research/crosslingual_winograd
- **论文**: "It's All in the Heads: Using Attention Heads as a Baseline for Cross-Lingual Transfer in Commonsense Reasoning"
- **加载方式**: 需要从GitHub克隆仓库

### 6. MMLU (Massive Multitask Language Understanding)
- **描述**: 大规模多任务语言理解数据集，涵盖57个学科
- **Hugging Face**: https://huggingface.co/datasets/cais/mmlu
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("cais/mmlu")
  ```

### 7. GSM8K (Grade School Math 8K)
- **描述**: 8.5K高质量小学数学应用题
- **Hugging Face**: https://huggingface.co/datasets/openai/gsm8k
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("openai/gsm8k")
  ```

### 8. MATH
- **描述**: 高中数学竞赛题数据集
- **Hugging Face**: https://huggingface.co/datasets/hendrycks/competition_math
- **备选**: https://huggingface.co/datasets/EleutherAI/hendrycks_math
- **备选**: https://huggingface.co/datasets/nlile/hendrycks-MATH-benchmark
- **GitHub**: https://github.com/hendrycks/math
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("hendrycks/competition_math")
  ```

### 9. BBH (Big-Bench Hard)
- **描述**: 困难任务集合，测试复杂推理能力
- **Hugging Face**: https://huggingface.co/datasets/lukaemon/bbh
- **大小**: 647 kB，6,511行数据
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("lukaemon/bbh")
  ```

### 10. HumanEval
- **描述**: 164个编程问题，用于评估代码生成能力
- **Hugging Face**: https://huggingface.co/datasets/openai/openai_humaneval
- **GitHub**: https://github.com/openai/human-eval
- **多语言版本**: https://huggingface.co/datasets/bigcode/humanevalpack
- **加载方式**: 
  ```python
  from datasets import load_dataset
  dataset = load_dataset("openai/openai_humaneval")
  ```

## 批量下载脚本

```python
from datasets import load_dataset
import os

# 创建数据目录
os.makedirs("evaluation_datasets", exist_ok=True)

# 数据集列表
datasets_info = {
    "openbookqa": "allenai/openbookqa",
    "triviaqa": "mandarjoshi/trivia_qa", 
    "hellaswag": "Rowan/hellaswag",
    "squad_v2": "rajpurkar/squad_v2",
    "mmlu": "cais/mmlu",
    "gsm8k": "openai/gsm8k",
    "math": "hendrycks/competition_math",
    "bbh": "lukaemon/bbh",
    "humaneval": "openai/openai_humaneval"
}

# 批量下载
for name, dataset_id in datasets_info.items():
    print(f"正在下载 {name}...")
    try:
        dataset = load_dataset(dataset_id)
        dataset.save_to_disk(f"evaluation_datasets/{name}")
        print(f"✓ {name} 下载完成")
    except Exception as e:
        print(f"✗ {name} 下载失败: {e}")

print("所有数据集下载完成！")
```

## 评测框架

### 推荐使用的评测工具：

1. **lm-evaluation-harness**: https://github.com/EleutherAI/lm-evaluation-harness
2. **OpenCompass**: https://github.com/open-compass/opencompass
3. **Swallow Evaluation**: https://github.com/swallow-llm/swallow-evaluation

## 注意事项

1. **存储空间**: TriviaQA较大(2.67GB)，请确保有足够存储空间
2. **网络连接**: 某些数据集较大，建议在稳定网络环境下载载
3. **许可证**: 使用前请查看各数据集的许可证要求
4. **版本**: 建议使用最新版本的datasets库 (`pip install datasets --upgrade`)

## Qwen3-4B在这些数据集上的表现

| 数据集 | 得分 | 说明 |
|--------|------|------|
| OpenBookQA | 0.382 | 科学知识问答 |
| TriviaQA | 0.508 | 知识问答 |
| HellaSwag | 0.555 | 常识推理 |
| SQuAD2 | 0.588 | 阅读理解 |
| XWINO | 0.891 | 代词指代推理 |
| MMLU | 0.729 | 多任务理解 |
| GSM8K | 0.719 | 数学应用题 |
| MATH | 0.520 | 高中数学 |
| BBH | 0.594 | 困难推理任务 |
| HumanEval | 0.617 | 代码生成 |

---
*文件创建时间: 2025-08-02*
*数据来源: Hugging Face Datasets, GitHub, 官方论文*
