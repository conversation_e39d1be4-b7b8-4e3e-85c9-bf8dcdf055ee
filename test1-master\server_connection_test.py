#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器连接测试脚本
用于验证*************:8080服务器的连接状态和模型服务可用性
"""

import requests
import json
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ServerConnectionTester:
    """服务器连接测试器"""

    def __init__(self, server_url: str = "http://*************:8080"):
        """
        初始化连接测试器

        Args:
            server_url: 服务器地址
        """
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def test_basic_connection(self) -> dict:
        """测试基本连接"""
        logger.info(f"测试基本连接到: {self.server_url}")
        result = {
            "test_name": "基本连接测试",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "response_time": None
        }

        try:
            start_time = time.time()
            response = self.session.get(f"{self.server_url}/", timeout=10)
            end_time = time.time()

            result["response_time"] = round((end_time - start_time) * 1000, 2)  # ms
            result["status_code"] = response.status_code
            result["success"] = response.status_code in [200, 404]  # 404也表示服务器在运行

            if result["success"]:
                logger.info(f"✅ 基本连接成功，状态码: {response.status_code}, 响应时间: {result['response_time']}ms")
            else:
                logger.warning(f"⚠️ 连接异常，状态码: {response.status_code}")

        except requests.exceptions.ConnectTimeout:
            result["error"] = "连接超时"
            logger.error("❌ 连接超时")
        except requests.exceptions.ConnectionError as e:
            result["error"] = f"连接错误: {str(e)}"
            logger.error(f"❌ 连接错误: {e}")
        except Exception as e:
            result["error"] = f"未知错误: {str(e)}"
            logger.error(f"❌ 未知错误: {e}")

        return result

    def test_model_health(self) -> dict:
        """测试模型健康状态"""
        logger.info("测试模型健康状态")
        result = {
            "test_name": "模型健康检查",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "response_time": None
        }

        try:
            test_data = {
                "messages": [{"role": "user", "content": "你好"}],
                "stream": False
            }

            start_time = time.time()
            response = self.session.post(f"{self.server_url}/rkllm_chat", json=test_data, timeout=15)
            end_time = time.time()

            result["response_time"] = round((end_time - start_time) * 1000, 2)  # ms
            result["status_code"] = response.status_code
            result["success"] = response.status_code == 200

            if result["success"]:
                response_data = response.json()
                result["model_response"] = response_data.get("response", "")[:100]  # 前100字符
                logger.info(f"✅ 模型健康检查成功，响应时间: {result['response_time']}ms")
                logger.info(f"模型响应: {result['model_response']}")
            else:
                result["error"] = f"HTTP {response.status_code}: {response.text[:200]}"
                logger.error(f"❌ 模型健康检查失败，状态码: {response.status_code}")

        except requests.exceptions.Timeout:
            result["error"] = "请求超时"
            logger.error("❌ 模型请求超时")
        except requests.exceptions.ConnectionError as e:
            result["error"] = f"连接错误: {str(e)}"
            logger.error(f"❌ 模型连接错误: {e}")
        except json.JSONDecodeError:
            result["error"] = "响应格式错误"
            logger.error("❌ 模型响应格式错误")
        except Exception as e:
            result["error"] = f"未知错误: {str(e)}"
            logger.error(f"❌ 模型测试未知错误: {e}")

        return result

    def test_performance_endpoint(self) -> dict:
        """测试性能监控端点"""
        logger.info("测试性能监控端点")
        result = {
            "test_name": "性能监控端点测试",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "response_time": None
        }

        try:
            start_time = time.time()
            response = self.session.get(f"{self.server_url}/metrics", timeout=10)
            end_time = time.time()

            result["response_time"] = round((end_time - start_time) * 1000, 2)  # ms
            result["status_code"] = response.status_code
            result["success"] = response.status_code == 200

            if result["success"]:
                logger.info(f"✅ 性能监控端点可用，响应时间: {result['response_time']}ms")
            else:
                result["error"] = f"HTTP {response.status_code}"
                logger.warning(f"⚠️ 性能监控端点不可用，状态码: {response.status_code}")

        except Exception as e:
            result["error"] = f"错误: {str(e)}"
            logger.warning(f"⚠️ 性能监控端点测试失败: {e}")

        return result

    def run_full_test(self) -> dict:
        """运行完整的连接测试"""
        logger.info("=" * 60)
        logger.info("开始服务器连接测试")
        logger.info("=" * 60)

        test_results = {
            "server_url": self.server_url,
            "test_timestamp": datetime.now().isoformat(),
            "tests": []
        }

        # 1. 基本连接测试
        basic_result = self.test_basic_connection()
        test_results["tests"].append(basic_result)

        # 2. 模型健康检查（只有基本连接成功才进行）
        if basic_result["success"]:
            model_result = self.test_model_health()
            test_results["tests"].append(model_result)
        else:
            logger.warning("⚠️ 跳过模型健康检查（基本连接失败）")

        # 3. 性能监控端点测试
        perf_result = self.test_performance_endpoint()
        test_results["tests"].append(perf_result)

        # 汇总结果
        successful_tests = sum(1 for test in test_results["tests"] if test["success"])
        total_tests = len(test_results["tests"])

        test_results["summary"] = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": round((successful_tests / total_tests) * 100, 1),
            "overall_status": "健康" if successful_tests >= 2 else "异常"
        }

        logger.info("=" * 60)
        logger.info(f"测试完成: {successful_tests}/{total_tests} 项通过 ({test_results['summary']['success_rate']}%)")
        logger.info(f"整体状态: {test_results['summary']['overall_status']}")
        logger.info("=" * 60)

        return test_results

def main():
    """主函数"""
    tester = ServerConnectionTester("http://*************:8080")
    results = tester.run_full_test()

    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"results/server_connection_test_{timestamp}.json"

    try:
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"测试结果已保存到: {result_file}")
    except Exception as e:
        logger.error(f"保存测试结果失败: {e}")

    return results

if __name__ == "__main__":
    main()