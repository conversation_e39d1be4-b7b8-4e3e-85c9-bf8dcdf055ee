#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RK3588 Qwen3-4B 评测系统主运行脚本
支持快速测试、完整评测和性能监控
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from typing import Optional

# 导入自定义模块
from evaluation_datasets import EvaluationDatasets
from api_client import R<PERSON><PERSON>MAPIClient
from quick_test import QuickEvaluator
from qwen3_4b_evaluation import ComprehensiveEvaluator
from performance_monitor import PerformanceMonitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'evaluation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask', 'requests', 'datasets', 'psutil', 'numpy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    return True

def setup_environment(data_dir: str):
    """设置环境"""
    # 创建必要的目录
    os.makedirs(data_dir, exist_ok=True)
    os.makedirs("results", exist_ok=True)
    os.makedirs("reports", exist_ok=True)
    
    logger.info(f"数据目录: {data_dir}")
    logger.info("结果目录: results/")
    logger.info("报告目录: reports/")

def download_datasets(data_dir: str, datasets: Optional[list] = None):
    """下载数据集"""
    logger.info("=== 下载评测数据集 ===")
    
    datasets_manager = EvaluationDatasets(data_dir)
    
    if datasets:
        logger.info(f"下载指定数据集: {', '.join(datasets)}")
        for dataset_name in datasets:
            if dataset_name in datasets_manager.datasets_config:
                try:
                    dataset = datasets_manager.load_dataset(dataset_name)
                    if dataset:
                        logger.info(f"✓ {dataset_name} 下载/加载成功")
                    else:
                        logger.error(f"✗ {dataset_name} 下载/加载失败")
                except Exception as e:
                    logger.error(f"✗ {dataset_name} 下载失败: {e}")
            else:
                logger.error(f"未知数据集: {dataset_name}")
    else:
        logger.info("下载所有数据集...")
        results = datasets_manager.download_all_datasets()
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"下载完成: {success_count}/{total_count} 个数据集成功")

def run_quick_test(server_url: str, data_dir: str):
    """运行快速测试"""
    logger.info("=== 运行快速验证测试 ===")
    
    # 初始化组件
    api_client = RKLLMAPIClient(base_url=server_url)
    datasets_manager = EvaluationDatasets(data_dir)
    evaluator = QuickEvaluator(api_client, datasets_manager)
    
    try:
        # 运行测试
        results = evaluator.run_quick_test()
        
        if "error" in results:
            logger.error(f"快速测试失败: {results['error']}")
            return False
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"results/quick_test_{timestamp}.json"
        
        import json
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"快速测试完成，结果保存到: {result_file}")
        
        # 显示摘要
        if "summary" in results:
            summary = results["summary"]
            logger.info(f"平均保持率: {summary.get('average_retention_rate', 0):.1%}")
            logger.info(f"测试数据集: {summary.get('datasets_tested', 0)}个")
        
        return True
        
    except Exception as e:
        logger.error(f"快速测试过程中发生错误: {e}")
        return False
    finally:
        api_client.close()

def run_comprehensive_evaluation(server_url: str, data_dir: str, include_performance: bool = False):
    """运行完整评测"""
    logger.info("=== 运行完整智商评测 ===")
    
    # 初始化组件
    api_client = RKLLMAPIClient(base_url=server_url)
    datasets_manager = EvaluationDatasets(data_dir)
    evaluator = ComprehensiveEvaluator(api_client, datasets_manager)
    
    try:
        # 运行评测
        results = evaluator.run_comprehensive_evaluation()
        
        if "error" in results:
            logger.error(f"完整评测失败: {results['error']}")
            return False
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"results/comprehensive_evaluation_{timestamp}.json"
        
        import json
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"完整评测完成，结果保存到: {result_file}")
        
        # 生成报告
        if include_performance:
            # 包含性能测试的完整报告
            test_prompts = [
                "请介绍一下人工智能的发展历史。",
                "解释一下机器学习和深度学习的区别。",
                "什么是大语言模型？它有哪些应用？",
                "请计算 123 + 456 = ?",
                "写一个Python函数来计算斐波那契数列。"
            ]
            performance_metrics = evaluator.performance_monitor.measure_inference_performance(test_prompts)
            report = evaluator.performance_monitor.generate_performance_report(performance_metrics, results)
        else:
            # 仅智商评测报告
            from qwen3_4b_evaluation import generate_evaluation_report
            report = generate_evaluation_report(results)
        
        report_file = f"reports/evaluation_report_{timestamp}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"评测报告已生成: {report_file}")
        
        # 显示摘要
        if "summary" in results:
            summary = results["summary"]
            logger.info(f"智商等级: {summary.get('iq_level', 'N/A')}")
            logger.info(f"平均保持率: {summary.get('average_retention_rate', 0):.1%}")
            logger.info(f"测试数据集: {summary.get('datasets_tested', 0)}个")
        
        return True
        
    except Exception as e:
        logger.error(f"完整评测过程中发生错误: {e}")
        return False
    finally:
        api_client.close()

def run_performance_test(server_url: str):
    """运行性能测试"""
    logger.info("=== 运行性能测试 ===")
    
    api_client = RKLLMAPIClient(base_url=server_url)
    monitor = PerformanceMonitor(api_client)
    
    try:
        # 测试提示
        test_prompts = [
            "请介绍一下人工智能的发展历史。",
            "解释一下机器学习和深度学习的区别。",
            "什么是大语言模型？它有哪些应用？",
            "请计算 123 + 456 = ?",
            "写一个Python函数来计算斐波那契数列。"
        ]
        
        # 测量性能
        metrics = monitor.measure_inference_performance(test_prompts)
        
        # 生成报告
        report = monitor.generate_performance_report(metrics)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"reports/performance_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"性能测试完成，报告保存到: {report_file}")
        
        # 显示关键指标
        logger.info(f"首Token延迟: {metrics.first_token_latency:.1f}ms")
        logger.info(f"Token生成速度: {metrics.token_generation_speed:.1f} tokens/s")
        logger.info(f"内存占用: {metrics.model_memory_usage:.2f}GB")
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试过程中发生错误: {e}")
        return False
    finally:
        api_client.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RK3588 Qwen3-4B 评测系统")
    parser.add_argument("--mode", choices=["download", "quick", "full", "performance"], 
                       required=True, help="运行模式")
    parser.add_argument("--server-url", default="http://*************:8080",
                       help="RKLLM服务器地址")
    parser.add_argument("--data-dir", default="evaluation_datasets", 
                       help="数据集目录")
    parser.add_argument("--datasets", nargs="+", 
                       help="指定要下载的数据集（仅在download模式下有效）")
    parser.add_argument("--include-performance", action="store_true",
                       help="在完整评测中包含性能测试")
    parser.add_argument("--check-deps", action="store_true",
                       help="检查依赖包")
    
    args = parser.parse_args()
    
    # 检查依赖
    if args.check_deps or not check_dependencies():
        if not check_dependencies():
            sys.exit(1)
        else:
            logger.info("所有依赖包都已安装")
            return
    
    # 设置环境
    setup_environment(args.data_dir)
    
    # 根据模式执行相应操作
    if args.mode == "download":
        download_datasets(args.data_dir, args.datasets)
    
    elif args.mode == "quick":
        success = run_quick_test(args.server_url, args.data_dir)
        if not success:
            sys.exit(1)
    
    elif args.mode == "full":
        success = run_comprehensive_evaluation(args.server_url, args.data_dir, args.include_performance)
        if not success:
            sys.exit(1)
    
    elif args.mode == "performance":
        success = run_performance_test(args.server_url)
        if not success:
            sys.exit(1)
    
    logger.info("评测系统运行完成")

if __name__ == "__main__":
    main()
