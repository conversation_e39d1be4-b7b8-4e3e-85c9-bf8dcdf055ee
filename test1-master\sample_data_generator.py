#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例数据生成器
为Qwen3-1.7B评测提供本地测试数据，避免网络依赖
"""

import json
import os
from typing import Dict, List, Any

class SampleDataGenerator:
    """示例数据生成器"""
    
    def __init__(self, data_dir: str = "evaluation_datasets"):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
    
    def generate_all_samples(self):
        """生成所有数据集的示例数据"""
        print("正在生成示例数据集...")
        
        # 生成各个数据集的示例数据
        datasets = {
            "openbookqa": self._generate_openbookqa_samples(),
            "triviaqa": self._generate_triviaqa_samples(),
            "hellaswag": self._generate_hellaswag_samples(),
            "squad_v2": self._generate_squad_v2_samples(),
            "mmlu": self._generate_mmlu_samples(),
            "gsm8k": self._generate_gsm8k_samples(),
            "math": self._generate_math_samples(),
            "bbh": self._generate_bbh_samples(),
            "humaneval": self._generate_humaneval_samples(),
            "xwino": self._generate_xwino_samples()
        }
        
        # 保存到文件
        for dataset_name, samples in datasets.items():
            file_path = os.path.join(self.data_dir, f"{dataset_name}_samples.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(samples, f, ensure_ascii=False, indent=2)
            print(f"✓ 生成 {dataset_name}: {len(samples)} 个样本")
        
        print(f"所有示例数据已保存到: {self.data_dir}")
    
    def _generate_openbookqa_samples(self) -> List[Dict[str, Any]]:
        """生成OpenBookQA示例数据"""
        return [
            {
                "question": "Which property of a mineral can be determined just by looking at it?",
                "choices": ["A) hardness", "B) color", "C) melting point", "D) density"],
                "answer": "B",
                "type": "multiple_choice"
            },
            {
                "question": "What happens to water when it freezes?",
                "choices": ["A) it becomes gas", "B) it becomes solid", "C) it becomes plasma", "D) it disappears"],
                "answer": "B",
                "type": "multiple_choice"
            },
            {
                "question": "Which of these is a renewable energy source?",
                "choices": ["A) coal", "B) oil", "C) solar", "D) natural gas"],
                "answer": "C",
                "type": "multiple_choice"
            },
            {
                "question": "What is the main gas in Earth's atmosphere?",
                "choices": ["A) oxygen", "B) nitrogen", "C) carbon dioxide", "D) hydrogen"],
                "answer": "B",
                "type": "multiple_choice"
            },
            {
                "question": "Which organ in the human body produces insulin?",
                "choices": ["A) liver", "B) kidney", "C) pancreas", "D) heart"],
                "answer": "C",
                "type": "multiple_choice"
            }
        ]
    
    def _generate_triviaqa_samples(self) -> List[Dict[str, Any]]:
        """生成TriviaQA示例数据"""
        return [
            {
                "question": "What is the capital of France?",
                "answer": "Paris",
                "type": "open_qa"
            },
            {
                "question": "Who wrote the novel '1984'?",
                "answer": "George Orwell",
                "type": "open_qa"
            },
            {
                "question": "What is the largest planet in our solar system?",
                "answer": "Jupiter",
                "type": "open_qa"
            },
            {
                "question": "In which year did World War II end?",
                "answer": "1945",
                "type": "open_qa"
            },
            {
                "question": "What is the chemical symbol for gold?",
                "answer": "Au",
                "type": "open_qa"
            }
        ]
    
    def _generate_hellaswag_samples(self) -> List[Dict[str, Any]]:
        """生成HellaSwag示例数据"""
        return [
            {
                "context": "A woman is sitting at a piano.",
                "choices": [
                    "She starts playing a beautiful melody.",
                    "She begins to eat the piano keys.",
                    "She transforms into a bird.",
                    "She starts speaking to the piano."
                ],
                "answer": "She starts playing a beautiful melody.",
                "type": "sentence_completion"
            },
            {
                "context": "A man is cooking in the kitchen.",
                "choices": [
                    "He adds ingredients to the pot.",
                    "He starts dancing with the stove.",
                    "He begins to eat the raw ingredients.",
                    "He throws the pot out the window."
                ],
                "answer": "He adds ingredients to the pot.",
                "type": "sentence_completion"
            },
            {
                "context": "Children are playing in the park.",
                "choices": [
                    "They run around and laugh together.",
                    "They start building a rocket ship.",
                    "They begin to study quantum physics.",
                    "They transform into adults instantly."
                ],
                "answer": "They run around and laugh together.",
                "type": "sentence_completion"
            }
        ]
    
    def _generate_squad_v2_samples(self) -> List[Dict[str, Any]]:
        """生成SQuAD2示例数据"""
        return [
            {
                "context": "The Amazon rainforest is the largest tropical rainforest in the world, covering much of the Amazon Basin in South America. It spans across nine countries, with the majority located in Brazil.",
                "question": "Which country contains the majority of the Amazon rainforest?",
                "answer": "Brazil",
                "is_impossible": False,
                "type": "reading_comprehension"
            },
            {
                "context": "Python is a high-level programming language known for its simplicity and readability. It was created by Guido van Rossum and first released in 1991.",
                "question": "Who created the Python programming language?",
                "answer": "Guido van Rossum",
                "is_impossible": False,
                "type": "reading_comprehension"
            },
            {
                "context": "The Great Wall of China is a series of fortifications built across the historical northern borders of China.",
                "question": "What is the height of the Great Wall of China?",
                "answer": "",
                "is_impossible": True,
                "type": "reading_comprehension"
            },
            {
                "context": "Artificial intelligence (AI) refers to the simulation of human intelligence in machines that are programmed to think and learn like humans.",
                "question": "What does AI stand for?",
                "answer": "Artificial intelligence",
                "is_impossible": False,
                "type": "reading_comprehension"
            }
        ]
    
    def _generate_mmlu_samples(self) -> List[Dict[str, Any]]:
        """生成MMLU示例数据"""
        return [
            {
                "question": "What is the derivative of x^2?",
                "choices": ["A) x", "B) 2x", "C) x^2", "D) 2"],
                "answer": "B",
                "subject": "mathematics",
                "type": "multiple_choice"
            },
            {
                "question": "Which of the following is a primary color?",
                "choices": ["A) green", "B) orange", "C) red", "D) purple"],
                "answer": "C",
                "subject": "art",
                "type": "multiple_choice"
            },
            {
                "question": "What is the capital of Japan?",
                "choices": ["A) Osaka", "B) Tokyo", "C) Kyoto", "D) Hiroshima"],
                "answer": "B",
                "subject": "geography",
                "type": "multiple_choice"
            },
            {
                "question": "Who developed the theory of relativity?",
                "choices": ["A) Newton", "B) Einstein", "C) Galileo", "D) Hawking"],
                "answer": "B",
                "subject": "physics",
                "type": "multiple_choice"
            }
        ]
    
    def _generate_gsm8k_samples(self) -> List[Dict[str, Any]]:
        """生成GSM8K示例数据"""
        return [
            {
                "question": "Janet's ducks lay 16 eggs per day. She eats 3 for breakfast every morning and bakes 4 into muffins for her friends every day. She sells the remainder at the farmers' market daily for $2 per fresh duck egg. How much in dollars does she make every day at the farmers' market?",
                "answer": "Janet's ducks lay 16 eggs per day. She eats 3 and bakes 4, so she uses 3 + 4 = 7 eggs. She has 16 - 7 = 9 eggs left to sell. At $2 per egg, she makes 9 * 2 = $18 every day.",
                "type": "math_word_problem"
            },
            {
                "question": "A store sells pencils for $0.25 each and erasers for $0.50 each. If Tom buys 8 pencils and 3 erasers, how much does he spend in total?",
                "answer": "Tom buys 8 pencils at $0.25 each, so he spends 8 * 0.25 = $2.00 on pencils. He buys 3 erasers at $0.50 each, so he spends 3 * 0.50 = $1.50 on erasers. In total, he spends $2.00 + $1.50 = $3.50.",
                "type": "math_word_problem"
            },
            {
                "question": "There are 24 students in a class. If 1/3 of them are boys, how many girls are in the class?",
                "answer": "1/3 of 24 students are boys, so there are 24 * (1/3) = 8 boys. The number of girls is 24 - 8 = 16 girls.",
                "type": "math_word_problem"
            },
            {
                "question": "A rectangle has a length of 12 cm and a width of 8 cm. What is its area?",
                "answer": "The area of a rectangle is length × width. So the area is 12 × 8 = 96 square cm.",
                "type": "math_word_problem"
            }
        ]
    
    def _generate_math_samples(self) -> List[Dict[str, Any]]:
        """生成MATH示例数据"""
        return [
            {
                "problem": "Find the value of x if 2x + 5 = 13.",
                "solution": "2x + 5 = 13\n2x = 13 - 5\n2x = 8\nx = 4",
                "level": "Level 1",
                "type": "math_competition"
            },
            {
                "problem": "What is the sum of the first 10 positive integers?",
                "solution": "The sum of the first n positive integers is n(n+1)/2. For n=10: 10(11)/2 = 55.",
                "level": "Level 2",
                "type": "math_competition"
            },
            {
                "problem": "Solve for x: x^2 - 5x + 6 = 0",
                "solution": "x^2 - 5x + 6 = 0\n(x - 2)(x - 3) = 0\nSo x = 2 or x = 3",
                "level": "Level 3",
                "type": "math_competition"
            }
        ]
    
    def _generate_bbh_samples(self) -> List[Dict[str, Any]]:
        """生成BBH示例数据"""
        return [
            {
                "input": "If all roses are flowers and all flowers are plants, then all roses are plants.",
                "target": "This is a valid logical argument using syllogism.",
                "task": "logical_reasoning",
                "type": "hard_reasoning"
            },
            {
                "input": "A bat and a ball cost $1.10 in total. The bat costs $1.00 more than the ball. How much does the ball cost?",
                "target": "The ball costs $0.05. If the ball costs x, then the bat costs x + $1.00. So x + (x + $1.00) = $1.10, which gives 2x = $0.10, so x = $0.05.",
                "task": "mathematical_reasoning",
                "type": "hard_reasoning"
            }
        ]
    
    def _generate_humaneval_samples(self) -> List[Dict[str, Any]]:
        """生成HumanEval示例数据"""
        return [
            {
                "task_id": "HumanEval/0",
                "prompt": "def has_close_elements(numbers, threshold):\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n",
                "canonical_solution": "    for idx, elem in enumerate(numbers):\n        for idx2, elem2 in enumerate(numbers):\n            if idx != idx2:\n                distance = abs(elem - elem2)\n                if distance < threshold:\n                    return True\n\n    return False\n",
                "entry_point": "has_close_elements",
                "type": "code_generation"
            },
            {
                "task_id": "HumanEval/1",
                "prompt": "def separate_paren_groups(paren_string):\n    \"\"\" Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n    separate those group and return the list of those.\n    Separate groups are balanced (each open brace is properly closed) and not nested within each other\n    Ignore any spaces in the input string.\n    >>> separate_paren_groups('( ) (( )) (( )( ))')\n    ['()', '(())', '(()())']\n    \"\"\"\n",
                "canonical_solution": "    result = []\n    current_string = []\n    current_depth = 0\n\n    for c in paren_string:\n        if c == '(':\n            current_depth += 1\n            current_string.append(c)\n        elif c == ')':\n            current_depth -= 1\n            current_string.append(c)\n\n            if current_depth == 0:\n                result.append(''.join(current_string))\n                current_string = []\n\n    return result\n",
                "entry_point": "separate_paren_groups",
                "type": "code_generation"
            }
        ]
    
    def _generate_xwino_samples(self) -> List[Dict[str, Any]]:
        """生成XWINO示例数据"""
        return [
            {
                "sentence": "The trophy doesn't fit into the brown suitcase because it's too large.",
                "pronoun": "it",
                "option1": "trophy",
                "option2": "suitcase",
                "answer": "trophy",
                "type": "pronoun_resolution"
            },
            {
                "sentence": "The city councilmen refused the demonstrators a permit because they feared violence.",
                "pronoun": "they",
                "option1": "councilmen",
                "option2": "demonstrators",
                "answer": "councilmen",
                "type": "pronoun_resolution"
            },
            {
                "sentence": "The delivery truck hit the parked car because it was moving too fast.",
                "pronoun": "it",
                "option1": "truck",
                "option2": "car",
                "answer": "truck",
                "type": "pronoun_resolution"
            }
        ]

def main():
    """主函数"""
    generator = SampleDataGenerator()
    generator.generate_all_samples()
    print("\n✅ 示例数据生成完成！")
    print("现在可以运行评测系统了。")

if __name__ == "__main__":
    main()
