#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3-1.7B 服务器连接测试脚本
验证192.168.0.106:8080的Flask服务连接
"""

import sys
import time
import logging
from datetime import datetime
from api_client import RKLLMAPIClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_server_connection():
    """测试服务器连接"""
    print("=" * 60)
    print("Qwen3-1.7B 服务器连接测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务器地址: http://192.168.0.106:8080")
    print(f"Python版本: {sys.version}")
    print("-" * 60)
    
    # 初始化API客户端
    api_client = RKLLMAPIClient(base_url="http://192.168.0.106:8080")
    
    try:
        # 1. 健康检查
        print("1. 执行健康检查...")
        start_time = time.time()
        is_healthy = api_client.health_check()
        health_time = time.time() - start_time
        
        if is_healthy:
            print(f"   ✓ 服务器健康检查通过 ({health_time:.2f}s)")
        else:
            print(f"   ✗ 服务器健康检查失败 ({health_time:.2f}s)")
            return False
        
        # 2. 简单推理测试
        print("2. 执行简单推理测试...")
        test_prompt = "你好，请简单介绍一下你自己。"
        start_time = time.time()
        result = api_client.simple_chat(test_prompt)
        inference_time = time.time() - start_time
        
        if result.success:
            print(f"   ✓ 推理测试成功 ({inference_time:.2f}s)")
            print(f"   响应内容: {result.response[:100]}...")
            print(f"   延迟: {result.latency:.3f}s")
            print(f"   速度: {result.tokens_per_second:.1f} tokens/s")
        else:
            print(f"   ✗ 推理测试失败: {result.error_message}")
            return False
        
        # 3. 性能基准测试
        print("3. 执行性能基准测试...")
        test_prompts = [
            "1+1等于多少？",
            "请解释什么是人工智能。",
            "写一个简单的Python函数。"
        ]
        
        total_time = 0
        total_tokens = 0
        success_count = 0
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"   测试 {i}/3: {prompt[:20]}...")
            start_time = time.time()
            result = api_client.simple_chat(prompt)
            test_time = time.time() - start_time
            
            if result.success:
                success_count += 1
                total_time += test_time
                # 估算token数量（简单估算：中文1字符≈1token，英文1词≈1token）
                estimated_tokens = len(result.response)
                total_tokens += estimated_tokens
                print(f"     ✓ 成功 ({test_time:.2f}s, ~{estimated_tokens} tokens)")
            else:
                print(f"     ✗ 失败: {result.error_message}")
        
        # 4. 汇总结果
        print("-" * 60)
        print("测试结果汇总:")
        print(f"  健康检查: {'✓ 通过' if is_healthy else '✗ 失败'}")
        print(f"  推理测试: {'✓ 通过' if result.success else '✗ 失败'}")
        print(f"  性能测试: {success_count}/{len(test_prompts)} 成功")
        
        if success_count > 0:
            avg_time = total_time / success_count
            avg_speed = total_tokens / total_time if total_time > 0 else 0
            print(f"  平均延迟: {avg_time:.2f}s")
            print(f"  平均速度: {avg_speed:.1f} tokens/s")
        
        # 5. 连接状态评估
        if is_healthy and result.success and success_count >= 2:
            print("\n🎉 服务器连接状态: 优秀")
            print("   系统已准备好进行完整评测")
            return True
        elif is_healthy and success_count >= 1:
            print("\n⚠️  服务器连接状态: 良好")
            print("   可以进行评测，但可能存在性能问题")
            return True
        else:
            print("\n❌ 服务器连接状态: 不可用")
            print("   请检查服务器状态和网络连接")
            return False
            
    except Exception as e:
        print(f"\n❌ 连接测试过程中发生错误: {e}")
        return False
    finally:
        api_client.close()

def main():
    """主函数"""
    success = test_server_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 连接测试完成 - 系统准备就绪")
        print("可以继续执行智商评测和性能监控")
    else:
        print("❌ 连接测试失败 - 请检查服务器状态")
        print("建议:")
        print("1. 确认Flask服务器在192.168.0.106:8080正常运行")
        print("2. 检查网络连接和防火墙设置")
        print("3. 验证模型文件已正确加载")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
